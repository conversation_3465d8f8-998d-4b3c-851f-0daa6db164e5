# AI Server

基于FastAPI的AI服务后端项目。

## 功能特性

- FastAPI框架
- Poetry依赖管理
- 自动化测试
- 日志中间件
- CI/CD支持
- **零停机部署** - 基于Gunicorn的平滑重启

## 环境要求

- Python 3.10+
- Poetry

## 安装

1. 克隆项目
```bash
git clone [项目地址]
cd ai_server
```

2. 安装依赖
```bash
make install
```

## 使用

### 开发环境
```bash
# 安装依赖
make install

# 启动开发服务器
make run

# 启动生产服务器（本地测试）
make run-prod

# 格式化代码
make format

# 运行测试
make test

# 清理缓存
make clean
```

### 生产环境
```bash
# 平滑重启服务
make restart

# 检查服务状态
make status

# 监控服务
make monitor

# 查看监控日志
make logs

# 健康检查
make health
```

### Docker操作
```bash
# 构建Docker镜像
make docker-build

# 运行Docker容器
make docker-run

# 停止Docker容器
make docker-stop
```

### 帮助信息
```bash
# 查看所有可用命令
make help
```

## 开发指南

### 项目结构
```
ai_server/
├── app/
│   ├── api/        # API路由
│   ├── core/       # 核心配置
│   ├── middleware/ # 中间件
│   └── utils/      # 工具函数
├── tests/          # 测试文件
├── Makefile        # 项目命令
└── pyproject.toml  # 项目配置
```

### 开发流程

1. 创建新分支
2. 开发新功能
3. 运行测试
4. 提交代码
5. 创建合并请求

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
