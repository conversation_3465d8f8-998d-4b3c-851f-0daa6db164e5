from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, field_validator


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(description="服务状态")
    timestamp: float = Field(description="检查时间戳")
    version: str = Field(description="服务版本")
    environment: str = Field(description="运行环境")


class DifyWorkflowResult(BaseModel):
    """Dify工作流结果模型"""

    status: int
    data: Dict[str, Any] | None = None
    message: str | None = None


class FileUploadResult(BaseModel):
    """文件上传结果模型"""

    file_id: str
    filename: str
    size: int | None = None


class WorkflowConfig(BaseModel):
    """单个工作流配置模型"""

    name: str = Field(description="工作流名称")
    description: Optional[str] = Field(default=None, description="工作流描述")
    api_key: str = Field(description="工作流API密钥")
    enabled: bool = Field(default=True, description="是否启用")
    timeout: int = Field(default=300, description="超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")

    @field_validator("timeout")
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError("timeout必须大于0")
        return v

    @field_validator("max_retries")
    @classmethod
    def validate_max_retries(cls, v):
        if v < 0:
            raise ValueError("max_retries不能小于0")
        return v


class DifyConfig(BaseModel):
    """Dify平台配置模型"""

    api_url: str = Field(description="Dify API地址")
    user: str = Field(default="admin", description="Dify用户")
    default_timeout: int = Field(default=300, description="默认超时时间(秒)")
    default_max_retries: int = Field(default=3, description="默认最大重试次数")

    @field_validator("api_url")
    @classmethod
    def validate_api_url(cls, v):
        if not v.startswith(("http://", "https://")):
            raise ValueError("api_url必须以http://或https://开头")
        return v.rstrip("/")


class WorkflowsConfig(BaseModel):
    """工作流配置集合模型"""

    dify: DifyConfig = Field(description="Dify平台配置")
    workflows: Dict[str, WorkflowConfig] = Field(description="工作流配置字典")

    def get_workflow(self, workflow_type: str) -> Optional[WorkflowConfig]:
        """获取指定类型的工作流配置"""
        return self.workflows.get(workflow_type)

    def get_enabled_workflows(self) -> Dict[str, WorkflowConfig]:
        """获取所有启用的工作流配置"""
        return {k: v for k, v in self.workflows.items() if v.enabled}

    def get_workflow_by_key(self, key: str) -> Optional[WorkflowConfig]:
        """根据工作流key获取配置（与get_workflow方法相同，提供更明确的命名）"""
        return self.get_workflow(key)
